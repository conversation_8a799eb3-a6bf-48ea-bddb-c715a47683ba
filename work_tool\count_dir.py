import re
from math import floor


file_path = r"C:\Users\<USER>\Desktop\调试工具\log\input1.txt"
file_path.encode('utf-8')

with open(file_path,'r', encoding='utf-8') as f:
    log = f.read()

found_with_count = len(re.findall(r"found with", log))
dir_count = len(re.findall(r"dir", log)) 
indir_count = len(re.findall(r"indir", log))

print(f"found with occurred {found_with_count} times")
print(f"dir occurred {dir_count} times") 
print(f"indir occurred {indir_count} times")

ratio = (found_with_count-indir_count)/ found_with_count*100
print(f"Ratio of dir to found with: {ratio:.2f}")