import re
import openpyxl

wb = openpyxl.Workbook()
sheet = wb.active


# 组串名称	组串类型	额度功率	组串标识

headers = ["组串名称", "组串类型", "额度功率", "组串标识"]
sheet.append(headers)

group_type = '1'  
group_power = '555'
group_desc = 'szjcjg'

file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\jg1.txt"
# file_path = r"C:\Users\<USER>\Desktop\szjcId\全部\cg_by_all.txt"
file_path.encode('utf-8')

with open(file_path) as f:
    content = f.read()

pattern = r'(\d+-\d+-\d+):(.+),' 
key_groups = []
for line in content.split('\n'):
    line= re.sub(pattern, r'\1:\2', line)
    match = re.match(r'(\d+-\d+-\d+):(.*)', line)
    if match:
        key = match.group(1)
        key_groups.append(key)

spilt_group = {}
for key in key_groups:
         # key 1-1101-28 name 1101-28  group_name 1101-28
         name=re.sub("^(\d+-)", "", key)
         group_name=name.split("-")[0]
         group_count=name.split("-")[1]
         if len(group_name)== 4: 
          name=name[1:]
          save_name="NO"+name[0]+"-"+name[1:]
          print(save_name)
          row = [save_name, group_type, group_power, group_desc]  
          sheet.append(row)
         else:
             #先找一下，如果没有找到就存入完整的组串id：组串个数，找到了就取出求和，然后写入文件
            #  spilt_group[]
             real_group_name=group_name[:4]
             if(real_group_name in spilt_group):   
              real_group_count=int(group_count)+int(spilt_group[real_group_name])
            #   print(real_group_name)
            #   print(real_group_count)
              real_inpugt_name=real_group_name+"-"+str(real_group_count)
              real_inpugt_name=real_inpugt_name[1:]
              save_name="NO"+real_inpugt_name[0]+"-"+real_inpugt_name[1:]
              print(save_name)
              row = [save_name, group_type, group_power, group_desc]  
              sheet.append(row)
             else:
                 spilt_group[real_group_name]=group_count

write_file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\jg_group.xlsx"

wb.save(write_file_path)