import json
import os
from glob import glob

xhs_path = os.path.join('..', 'xhs')

# 读取所有xhs_list_new.txt文件
file_list = glob(os.path.join(xhs_path, 'list*.txt')) 

all_notes = []
for file in file_list:
    with open(file, encoding='utf-8') as f:
        data = json.load(f)
        
    items = data['data']['items']
    
    # 过滤并排序note
    notes = [item for item in items if item['model_type']=='note']
    all_notes.extend(notes)

    # ranked_notes = sorted(notes, key=lambda x: int(x['note_card']['interact_info']['liked_count']), reverse=True) 
    
    # all_notes.extend(ranked_notes)

all_notes_new = sorted(all_notes, key=lambda x: int(x['note_card']['interact_info']['liked_count']), reverse=True) 
html = '''
<html>
<head>
<style>
table {
  margin: auto;  
  border: 1px solid black;
  border-collapse: collapse;
}

td, th {
  border: 1px solid black;
  padding: 5px;
  text-align: center;  
}

a.link:link {
  color: gray;
}

a.link:visited {
  color: black;
}

</style>
</head>

<body>
<table>
<tr>
  <th>Index</th>
  <th>Display Title</th>
  <th>Liked Count</th>
  <th>Nick Name</th>  
</tr>
'''

index = 1
for note in all_notes_new:
    if 'display_title' in note['note_card']:
       display_title = note['note_card']['display_title']
       display_title_link = f"<a href='https://www.xiaohongshu.com/explore/{note['id']}' target='_blank' class='link'>{display_title}</a>"
    # 其他处理
    else:
      display_title = ''
      display_title_link=''
    # display_title = note['note_card']['display_title']
    
    nick_name = note['note_card']['user']['nick_name']
    user_id = note['note_card']['user']['user_id']
    nick_name_link = f"<a href='https://www.xiaohongshu.com/user/profile/{user_id}' target='_blank' class='link'>{nick_name}</a>"

    liked_count = note['note_card']['interact_info']['liked_count']
    
    html += f'''
<tr>
  <td>{index}</td>
  <td>{display_title_link}</td>
  <td>{liked_count}</td>
  <td>{nick_name_link}</td>  
</tr>
'''
    index += 1

html += '''  
</table>
</body>
</html>
'''

with open(os.path.join(xhs_path, 'output.html'), 'w', encoding='utf-8') as f:
    f.write(html)