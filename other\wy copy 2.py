import openpyxl

# wb = openpyxl.load_workbook('../input/wy66666.xlsx')

# print(wb.sheetnames)

# arrays = [
#     [503,604,404,603,408,808,807,507,705,202,804,501,505,304,605,803,602,806,407,206,204,306,208,606,401,303,601,703,405,607,307,207],
#     [404,705,703,501,301,303,702,101,803,604,203,401,504,805,105,602,601],
#     [802,203,401,503,404,303,601,301,703,204,803,501,604,804,402],
#     [102,201,202,301,302,304,402,403,501,503,504,601,604,701,703,704,801,802,803],
#     [602,504,303,702,305,403,503,102,802,202,204,705,604,402],
#     [305,302,205,701,502,404,704,403,603,804,503,501,805,604,504],
#     [303,603,504,204,301,602,604,501,404,503,403,102,402,202],
#     [502,703,604,207,605,505,306,601,303,603,401,706,501,707,504,606,105,103,305,203,407,304,406,503,506,202],
#     [208,407,102,506,805,307,101,703,304,507,602,408,107,105,607,402,503,504,606,605,707,404,804,202,502,206,103,706,802,401,308],
#     [202,401,701,101,301,501],
#     [704,304,401,702,404,601,303,101,603,103,203,502,804],
#     [601,504,704,205,105,102,603,802,204,805,501,801,201,101,403,803,602,401,502,701,203,604,505,702],
#     [203,502,802,201,504,303,103,604,204,702,202,304,401,502],
#     [303,202,705,405,302,203,605,704,102,403,105,702,101,703,201,603,805],
#     [401,101,604,802,703,602,702,204,302,504,404,303,803],
#     [207,804,506,502,705,406,605,306,504,606,302,402,203,706,301,404,405,203,407,607,805,304]
# ]

# count = 0


def fill_green(sheet, values):
    sigleCount = 0
    for row in sheet.iter_rows():
        for cell in row:
            # print(cell.value)
            if cell.value in values:
                # print(cell.value)
                cell.fill = openpyxl.styles.PatternFill(patternType="solid", fgColor="FFCC00")
                # count += 1
                sigleCount+=1
    # print(sigleCount)

    return sigleCount     
    

def main():
 wb1 = openpyxl.load_workbook('../input/uu.xlsx') 
 sheet = wb1.active
 prj = sheet.columns
 arrays = []


 prjTuple = tuple(prj)

 for idx in range(0,16):
    column = []
    for cell in prjTuple[idx]:
      if cell.value is not None:
    #    print(cell.coordinate, cell.value)
       column.append(cell.value)
    arrays.append(column)
    print("数组内容:")
    for i in range(len(arrays)):
     print("数组", i, ":", len(arrays[i]))
    # 打开 Excel 文件
    wb = openpyxl.load_workbook("../input/wy66666.xlsx")

    # 获取所有 sheet
    sheets = wb.worksheets
   
    for sheet_index, sheet in enumerate(sheets):
        print(sheet_index)
    # 循环 sheet
    # for sheet in sheets:
        # 获取 sheet 中的所有值
        values = sheet.values

        # 填充绿色
        # if(sheet_index<2)
        fill_green(sheet, arrays[sheet_index+1])

    # 保存 Excel 文件
    wb.save("../input/wy66666.xlsx")
    # print(count)


if __name__ == "__main__":
    main()

