# pip install feedparser  pip install beautifulsoup4
# mac 安装要加--trusted-host mirrors.aliyun.com pip install beautifulsoup4 --trusted-host mirrors.aliyun.com
import feedparser
from bs4 import BeautifulSoup
from datetime import datetime
import shutil

# b站热门 https://rss.mifaw.com/articles/5c8bb11a3c41f61efd36683e/5ca0144af6f83a0a176acfd6

def main():

# RSS订阅地址
    rss_url = 'https://rss.mifaw.com/articles/5c8bb11a3c41f61efd36683e/5cac99a7f5648c90ed310e18'  # 替换为你的RSS订阅地址

    # 解析RSS订阅
    feed = feedparser.parse(rss_url)

    # 打印返回的内容
    print("Feed Title:", feed.feed.title)
    print("Feed Link:", feed.feed.link)
    print("Feed Description:", feed.feed.description)

    # 定义原始字符串的格式
    date_format = '%a, %d %b %Y %H:%M:%S %z'

    # 定义目标格式
    target_format = '%Y-%m-%d-%H'
    wb_file_path=fr'D:\works\wb\wb-hot.txt'


    # 遍历所有条目
    for entry in feed.entries:
        print("\nEntry Title:", entry.title)
        print("Entry Link:", entry.link)
        print("Entry Published:", entry.published)
        date_object = datetime.strptime(entry.published, date_format)
        formatted_date_string = date_object.strftime(target_format)
        print(formatted_date_string)
        wb_file_path_back=fr'D:\works\wb\wb-hot-{formatted_date_string}.txt'

        # print("Entry Summary:", entry.summary)
        soup = BeautifulSoup(entry.summary, 'html.parser')
        a_tags = soup.find_all('a')
        with open(wb_file_path, 'w', encoding='utf-8') as file:
        # 遍历所有的<a>标签
            for tag in a_tags:
                # 获取链接的href属性
                href = tag.get('href')
                # 获取<a>标签中的文本
                link_text = tag.get_text()
                file.write(f"{link_text}\n{href}\n")
    shutil.copy(wb_file_path, wb_file_path_back)


if __name__ == "__main__":
    main()