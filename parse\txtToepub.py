import os
import re
from ebooklib import epub
from docx import Document

def convert_txt_to_html(txt_file):
    with open(txt_file, 'r', encoding='utf-8') as file:
        content = file.read()
    content=content.replace('\n','')
    paragraphs = re.split(r'[。！？]', content)
    segments = []
    segment = []
    for paragraph in paragraphs:
            if paragraph:
                segment.append(paragraph+'。')
                if len(segment) == 3:
                    segments.append(segment)
                    segment = []

    if segment:
        segments.append(segment)

    html_content = '<html><body>'
    for segment in segments:
        html_content += '<p>'+"".join(segment) + '</p>'
        html_content += '<br>'

    html_content += '</body></html>'
    return html_content

def convert_docx_to_html(docx_file):
    doc = Document(docx_file)
    paragraphs = []
    for paragraph in doc.paragraphs:
        paragraphs.append(paragraph.text)

    content = ' '.join(paragraphs)
    html_content = '<html><body><p>' + content + '</p></body></html>'
    return html_content

def merge_txt_to_epub(folder_path, output_file):
    book = epub.EpubBook()

    txt_files = [f for f in os.listdir(folder_path) if f.endswith('.txt')]

    for index, txt_file in enumerate(txt_files):
        file_path = os.path.join(folder_path, txt_file)
        file_name = os.path.splitext(os.path.basename(txt_file))[0]
        html_content = convert_txt_to_html(file_path)
        chapter = epub.EpubHtml(title=file_name, file_name='chap_{}.xhtml'.format(index + 1))
        chapter.content = html_content
        book.add_item(chapter)
        book.toc.append(chapter)

    book.add_item(epub.EpubNcx())
    book.add_item(epub.EpubNav())

    book.spine = ['nav'] + book.toc

    epub.write_epub(output_file, book, {})

if __name__ == '__main__':
    folder_path = '../txt'  # 要合并的txt文件所在的文件夹路径
    output_file = '../txt/plai.epub'  # 输出epub文件名

    merge_txt_to_epub(folder_path, output_file)
