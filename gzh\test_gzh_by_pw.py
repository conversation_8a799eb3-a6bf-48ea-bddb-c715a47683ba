import re
from playwright.sync_api import <PERSON><PERSON>, sync_playwright, expect
from env import GlobalVars

# pip install playwright
# playwright install
# playwright codegen -o test_script55.py  --save-storage=auth.json  https://mp.weixin.qq.com/
# playwright codegen -o test_script55.py  --load-storage=auth.json  https://mp.weixin.qq.com/

def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch_persistent_context(user_data_dir=GlobalVars.chrome_userdata_path1,channel="chrome", headless=False)

    # context = browser.new_context()
    page = browser.new_page()
    page.goto("https://mp.weixin.qq.com/")
    page.get_by_title("内容管理").locator("div").click()
    page.get_by_role("link", name="草稿箱").click()
    page.locator(".weui-desktop-card__icon-add").click()
    with page.expect_popup() as page1_info:
        page.get_by_role("link", name="写新文章").click()
    page1 = page1_info.value
    page1.get_by_placeholder("请在这里输入标题").click()
    page1.get_by_placeholder("请在这里输入标题").fill("u11899")
    page1.get_by_role("button", name="保存为草稿").click()
    # page1.close()
    input('Press Enter to close the browser...')


  
    # browser.close()


with sync_playwright() as playwright:
    run(playwright)
