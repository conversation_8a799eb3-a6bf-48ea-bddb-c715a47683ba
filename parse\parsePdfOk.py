import logging,os,html
from PyPDF2 import  PdfReader, PdfWriter,PdfMerger
file_writer = PdfWriter()
merger = PdfMerger()
num = 0
for root, dirs, files in os.walk('..\pdf'):
    for name in files:
        if name.endswith(".pdf"):
            print(name)
            file_reader = PdfReader(f"..\pdf\{name}")
            file_writer.add_outline_item(html.unescape(name).replace('.pdf',''), num, parent=None)
            for page in range(len(file_reader.pages)):
                num += 1
                file_writer.add_page(file_reader.pages[page])
with open(r"..\pdf\uuu2.pdf",'wb') as f:
    file_writer.write(f)