import re
import os
import json
from pathlib import Path
from playwright.sync_api import Playwright, sync_playwright, expect
from env import Global<PERSON><PERSON>


def run(playwright: Playwright) -> None:
    try:
        # 使用CDP连接到已经运行的Chrome浏览器（需要Chrome以特定方式启动）
        # 请先使用以下命令启动Chrome：
        # Mac: /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222
        # Windows: start chrome.exe --remote-debugging-port=9222
        browser = playwright.chromium.connect_over_cdp("http://127.0.0.1:9222")
        
        # 使用已有的上下文
        context = browser.contexts[0]
        
        # 创建新页面
        page = context.new_page()
        page.goto("https://mp.toutiao.com/profile_v4/index")
        print("已加载页面，检查是否已登录")
        page.wait_for_timeout(3000)  # 等待页面加载完成
        
        # 检查是否需要点击"文章"链接（如果已登录才会出现）
        if page.get_by_role("link", name="文章").is_visible():
            print("已检测到登录状态，继续执行")
            page.get_by_role("link", name="文章").click()
            page.wait_for_timeout(5000)
            
            # 填写文章标题和正文
            page.get_by_placeholder("请输入文章标题（2～30个字）").fill("你好")
            page.wait_for_timeout(1000)
            page.locator("div").filter(has_text=re.compile(r"^请输入正文$")).fill("你好1111")

            # 添加图片
            page.locator(".add-icon").click()
            page.get_by_role("button", name="本地上传").get_by_role("textbox").click()
            page.get_by_role("button", name="本地上传").get_by_role("textbox").set_input_files(["10001.webp", "10003.png"])
            page.get_by_role("button", name="确定").click()
            page.locator("g > circle").first.click()
        else:
            print("未检测到登录状态，请先在Chrome浏览器中登录头条账号")
            print("确保您使用的Chrome实例已经登录了头条账号")
            
        input('按回车键关闭浏览器...')
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        if 'browser' in locals():
            browser.close()


def print_cdp_launch_instructions():
    """打印如何启动支持CDP调试的Chrome浏览器的说明"""
    system = platform.system()
    print("请先按照以下说明启动Chrome浏览器以支持CDP连接:")
    if system == 'Darwin':  # macOS
        print("打开终端，执行以下命令:")
        print("/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222")
    elif system == 'Windows':
        print("打开命令提示符，执行以下命令:")
        print("start chrome.exe --remote-debugging-port=9222")
    elif system == 'Linux':
        print("打开终端，执行以下命令:")
        print("google-chrome --remote-debugging-port=9222")
    print("\n启动浏览器后，再运行此脚本连接到浏览器")


if __name__ == "__main__":
    import platform
    # print_cdp_launch_instructions()
    # input("已经按上述方式启动Chrome后，按回车键继续...")
    
    with sync_playwright() as playwright:
        GlobalVars.set_system_path()
        run(playwright) 