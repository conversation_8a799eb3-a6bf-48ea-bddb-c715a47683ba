import re

# file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\8_groupChipId.txt"
file_path = r"C:\Users\<USER>\Desktop\szjcId\cg1\cg1.txt"
file_path.encode('utf-8')

with open(file_path) as f:
    content = f.read()

pattern = r'(\d+-\d+):(.+),' 
data = {}
key_groups = []
current_prefix = None 
relay_values = []
for line in content.split('\n'):
    line= re.sub(pattern, r'\1:\2', line)
    # 获取最前面的前缀
    match = re.match(r'(\d+-\d+):(.*)', line)
    if match:
        key = match.group(1)
        values = [x.strip() for x in match.group(2).split(',')]
        data[key] = values
         # 把前缀相同的key放在一个数组中

all_values = set()
all_values_index = {}
duplicates = {}

for key in data.keys():
 for i, value in enumerate(data[key]):
         # 判断值是否存在，存在就判断是否存在于重复字典中，不存在就加入所有值中
          if value in all_values:
            # 判断值是否存在重复字典
            if value not in duplicates:
                duplicates[value]=all_values_index[value]
                duplicates[value].append((key,i+1))
            else:
                duplicates[value].append((key,i+1)) 
          else:    
            all_values.add(value)
            all_values_index[value]=[(key,i+1)]



# print(repeat_values)
# print(duplicates)
print(len(all_values))
for key in duplicates.keys():
    print(key, ":", duplicates[key])
