import openpyxl

wb = openpyxl.load_workbook('../input/uu.xlsx') 
sheet = wb.active
prj = sheet.columns
arrays = []


prjTuple = tuple(prj)

for idx in range(0,16):
    column = []
    for cell in prjTuple[idx]:
      if cell.value is not None:
    #    print(cell.coordinate, cell.value)
       column.append(cell.value)
    arrays.append(column)

    
print("数组内容:")
for i in range(len(arrays)):
  print("数组", i, ":", len(arrays[i]))