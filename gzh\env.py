import platform
from pathlib import Path
import os


class GlobalVars:

  base_bing_path= Path(fr'D:\git\bing-wallpaper\picture')
  base_jingju_path= Path(r'D:\works\jingju')
  chromedriver_path = 'D:\works\chromedriver-win64\chromedriver-win64\chromedriver.exe'
  chrome_userdata_path=r'user-data-dir=C:\Users\<USER>\AppData\Local\Google\Chrome\User Data'
  chrome_userdata_path1=r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data'
  gzh_template1_path= Path(r'D:\works\gzh\ar1_template.txt')
  gzh_template2_path= Path(r'D:\works\gzh\ar2_template.txt')
  gzh_data_path= Path(r'D:\works\gzh\data.txt')
  wb_template_path= Path(r'D:\works\wb\wb1_template.txt')
  wb_data_path= Path(r'D:\works\wb\wb-hot.txt')

  @classmethod
  def set_system_path(cls):
    system = platform.system()

    if system == 'Darwin':
        print("当前操作系统是Mac")
        cls.base_bing_path= Path(fr"/Users/<USER>/git/bing-wallpaper/picture")
        cls.base_jingju_path= Path("/Users/<USER>/work/jingju")
        cls.chromedriver_path='/Users/<USER>/work/chromedriver-mac-x64/chromedriver'
        
        # 创建一个专用的用户数据目录，以避免与正在运行的Chrome实例冲突
        # user_data_dir = os.path.expanduser("~/Project/pr_python/chrome_user_data")
        # os.makedirs(user_data_dir, exist_ok=True)
        # cls.chrome_userdata_path = user_data_dir

        cls.chrome_userdata_path=r"user-data-dir=/Users/<USER>/Library/Application Support/Google/Chrome"

        
        cls.gzh_template1_path=Path(r'/Users/<USER>/work/gzh/template.txt')
        cls.gzh_template2_path=Path(r'/Users/<USER>/work/gzh/template.txt')
        cls.gzh_data_path=Path(r'/Users/<USER>/work/gzh/data.txt')
        cls.wb_template_path= Path(r'/Users/<USER>/work/wb/wb1_template.txt')
        cls.wb_data_path= Path(r'/Users/<USER>/work/wb/wb-hot.txt')

  @classmethod
  def get_file_content(cls,file_path):
   with file_path.open('r', encoding='utf-8') as f:
      # 读取文件内容
      content = f.read()
      return content
             

    

