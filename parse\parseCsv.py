import csv

with open('scjh_url.csv', newline='', encoding='utf-8') as csvfile:
    reader = csv.reader(csvfile)
    with open('output.csv', 'w', newline='', encoding='utf-8') as outfile:
        writer = csv.writer(outfile)
        for row in reader:
            data = eval(row[0])
            topic_id = data['topic_id']
            title = data['title']
            writer.writerow([title, topic_id])

