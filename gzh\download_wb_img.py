from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from env import GlobalVars 
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
import time
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from pynput.keyboard import Key, Controller

# pip install pynput


def set_browser():
    service = Service(executable_path=GlobalVars.chromedriver_path)

    # 创建Chrome选项对象
    options = Options()

    # 设置Chrome选项，例如启动时最大化窗口
    options.add_argument("--start-maximized")
    options.add_argument(GlobalVars.chrome_userdata_path)

    # 程序结束后，不关闭浏览器
    options.add_experimental_option('detach', True)
    # 设置无头模式
    # options.add_argument('--headless=new') 
     # 禁用gpu加速
    # options.add_argument('--disable-gpu') 

    # 可以添加更多的选项，例如禁用图片来加速加载
    # options.add_argument("--disable-images")

    # 创建WebDriver实例
    browser = webdriver.Chrome(service=service, options=options)
    return browser



def handle_browser(write_str,browser):
    browser.get(write_str)

    # actions = ActionChains(browser)
    # actions.context_click().perform()
    # time.sleep(1)
    # for i in range(10):

    keyboard = Controller()
    keyboard.press(Key.alt)  # 按住Ctrl键
    keyboard.press('q')       # 按下快捷键Q
    keyboard.release('q')
    keyboard.release(Key.alt)
    time.sleep(5)
    current_windows = browser.window_handles
    for tab in current_windows:
        browser.switch_to.window(tab)
        if 'chrome-extension' in browser.current_url:
           buttons = browser.find_element(By.ID, r"select_menu")
           print(browser.current_url)

        #    button.click()

    # actions.send_keys(Keys.ARROW_DOWN)
    # actions.perform()
    # actions.send_keys(Keys.ENTER).send_keys(Keys.ENTER).perform()     

    # actions.key_down(Keys.CONTROL).send_keys('q').key_up(Keys.CONTROL).perform()

    # input_kw=browser.find_element(By.CLASS_NAME, r"editorContentEditable___FZJd9")
    # input_kw.send_keys(Keys.ENTER)
    # time.sleep(2)
    # input_kw.key_down(Keys.ALT).send_keys('q').key_up(Keys.ALT)


    # input_kw.send_keys('dflsjldf')

    # time.sleep(2)
    # input_kw.send_keys(Keys.CONTROL , 'q')



def handle_wb_img(browser):
    data_content=GlobalVars.get_file_content(GlobalVars.wb_data_path)
    data_lines=data_content.split('\n')
    for line in data_lines:
        if 'https' in line:
            print(line)
            handle_browser(line,browser)


def main():
    
   try: 
        GlobalVars.set_system_path()
        browser=set_browser()
        
        # write_gzh_ar(browser)
        handle_wb_img(browser)
        # browser.quit()
   except Exception as e:
          print(e) 
        #   browser.quit()        



if __name__ == "__main__":
    main()