import pandas as pd
import requests
import json
import hashlib

def translate_to_chinese(query):
    url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
    appid = ""  # 请替换为你的百度翻译API的appid
    secret_key = ""  # 请替换为你的百度翻译API的密钥
    salt = "999000"  # 请替换为你的随机盐值
    sign = hashlib.md5((appid + query + salt + secret_key).encode('utf-8')).hexdigest()  # 签名

    params = {
        "q": query,
        "from": "en",
        "to": "zh",
        "appid": appid,
        "salt": salt,
        "sign": sign
    }

    response = requests.get(url, params=params)
    result = json.loads(response.text)

    return result['trans_result'][0]['dst']

def process_csv(file_name):
    df = pd.read_csv(file_name)
    df['Folder Name'] = df['Folder Name'].str.replace('-', ' ')
    df['Folder Name'] = df['Folder Name'].apply(translate_to_chinese)
    df.to_csv('translated.csv', index=False)

process_csv('../input/file_organization.csv')
