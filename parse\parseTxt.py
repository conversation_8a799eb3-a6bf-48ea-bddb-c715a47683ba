import csv

csv_file = 'data.csv'  # 输入的CSV文件名
txt_file = 'output.txt'  # 输出的TXT文件名
special_string = 'https://wx.zsxq.com/dweb2/index/topic_detail/'  # 特定字符串

# 打开CSV文件并读取数据
with open("../input/title_id_update.csv", 'r', newline='', encoding='utf-8') as file:
    reader = csv.reader(file)
    data = list(reader)

# 生成TXT文件
with open('../output/sc.txt', 'w', encoding='utf-8') as file:
    for row in data:
        title = row[0]
        id_value = row[1]
        hyperlink = special_string + id_value
        line = f'{title}\n{hyperlink}\n'
        file.write(line)