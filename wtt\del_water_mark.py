import os
from PIL import Image  

# 设置包含图片的目录
img_dir = 'D:/works/gzh/图片助手(ImageAssistant)_批量图片下载器/m.weibo.cn/瑞幸加茅台华尔街跪下来' 

# 遍历目录下所有文件
for filename in os.listdir(img_dir):
    if filename.endswith('.jpg') or filename.endswith('.png'):

        # 打开图片,移除alpha通道
        image = Image.open(os.path.join(img_dir, filename))
        image = image.convert('RGB')

        # 保存移除水印后的新图片
        new_filename = filename.split('.')[0] + '-new.jpg' 
        image.save(os.path.join(img_dir, new_filename))

print('success!')