from docx import Document
from env import GlobalVars
from docx.shared import Inches


def read_file(file_path):
    with file_path.open('r', encoding='utf-8') as f:
      # 读取文件内容
      content = f.read()
       # 把文件内容按行分割
      lines=content.split('\n')
    return lines

       
def save_docx(txt_lines,img_files):
    
     # 创建一个Word文档
    doc = Document()
    img_num=len(img_files)
    # 添加一个段落并设置字体和大小
    for line_index,line in enumerate(txt_lines):
        p = doc.add_paragraph()
        p.add_run(line).font.name= 'Arial'
        if(line_index<img_num):
            # 添加图片，这里需要提供图片的路径 每一行字后添加一张图片  
            # width=Inches(2.5) 表示图片宽度是2.5英寸
            doc.add_picture(str(img_files[line_index].resolve()), width=Inches(5.5))
            # doc.add_picture(str(img_files[line_index].resolve()), width=None)
             # 每篇文章写入特定的条数就保存
        if(line_index>=5 and line_index%5==0):
            save_filename=f'jingju{line_index}.docx'
            file_path = GlobalVars.base_jingju_path.joinpath(save_filename)
            # 保存文档
            doc.save(file_path.resolve())
            doc = Document()
             

            


def main():
 
 GlobalVars.set_system_path()
 jingju_img_path = GlobalVars.base_jingju_path.joinpath('img_new').joinpath('2021-05')

 img_files=list(jingju_img_path.glob('*.jpg'))
 txt_file_path = GlobalVars.base_jingju_path.joinpath('jingju.txt')

 txt_lines=read_file(txt_file_path)
 save_docx(txt_lines,img_files)

if __name__ == "__main__":
    main()

