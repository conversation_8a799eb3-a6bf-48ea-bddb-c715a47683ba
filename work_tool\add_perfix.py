
input_file = r"C:\Users\<USER>\Desktop\jg1.txt"
output_file = r"C:\Users\<USER>\Desktop\jg1_perfix.txt"

with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
    for line in f_in:
        if line.strip():  # 检查行是否不为空
            new_line = 'j' + line
        else:
            new_line = line
        f_out.write(new_line)

print(f"Lines in {input_file} have been prefixed with 'c' and written to {output_file}")