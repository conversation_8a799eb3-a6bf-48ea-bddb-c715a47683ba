import openpyxl

wb = openpyxl.Workbook()
sheet = wb.active

headers = ["名称1", "名称2", "名称3", "名称4", "名称5", "名称6"]
sheet.append(headers)

name1 = 'a'  
name3 = 'b'
name6 = 'c'

name4_values = ['val1', 'val2', 'val3']  

for value4 in name4_values:
    value2 = value4   
    value5 = '0000' + value4
    
    row = [name1, value2, name3, value4, value5, name6]  
    sheet.append(row)
    
wb.save('output.xlsx')