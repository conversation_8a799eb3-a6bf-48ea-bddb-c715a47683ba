from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from env import GlobalVars 
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from datetime import datetime


# 指定ChromeDriver的路径
def handle_browser():

    # 创建Chrome服务对象
    service = Service(executable_path=GlobalVars.chromedriver_path)

    # 创建Chrome选项对象
    options = Options()

    # 设置Chrome选项，例如启动时最大化窗口
    options.add_argument("--start-maximized")
    options.add_argument('--ignore-certificate-errors')
    # 程序结束后，不关闭浏览器
    options.add_experimental_option('detach', True)

    # 可以添加更多的选项，例如禁用图片来加速加载
    # options.add_argument("--disable-images")

    # 创建WebDriver实例
    browser = webdriver.Chrome(service=service, options=options)

    # 打开网页
    url = 'http://www.baidu.com'
    browser.get(url)
    input_kw=browser.find_element(by= By.ID, value="kw")
    input_kw.send_keys('你好')
    # 显式等待
    wait = WebDriverWait(browser,3)

     # 等待一个特定元素可点击
    element = wait.until(EC.element_to_be_clickable((By.ID, 'su')))
    element.click()
    # 打印时间
    current_time = datetime.now()
    formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
    print("格式化的当前时间：", formatted_time)
    time.sleep(10)
   
    browser.quit()
    

# 进行一些操作，例如获取页面标题
# print(browser.title)
# browser.close()

# 关闭浏览器
# browser.quit()

def main():
    GlobalVars.set_system_path()
    handle_browser()

if __name__ == "__main__":
    main()