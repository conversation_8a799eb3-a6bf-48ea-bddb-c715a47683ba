import os
import shutil
import csv

# 源文件夹路径
source_folder = r'C:\Users\<USER>\Downloads\images13'

# 目标文件夹路径
target_folder = r'C:\Users\<USER>\Downloads\images131'

# CSV文件路径
csv_file = r'C:\Users\<USER>\Downloads\images131\output.csv'

# 获取源文件夹中所有文件
files = os.listdir(source_folder)

# 字典用于存储相同文件名的文件列表
file_dict = {}

# 遍历所有文件
for file_name in files:
    # 去掉最前面的数字和破折号
    new_file_name = file_name.split('-', 1)[-1]
    
    # 检查文件名是否已经在字典中
    if new_file_name in file_dict:
        # 文件名已存在，将文件移动到相应文件夹
        folder_path = file_dict[new_file_name][0]
        file_path = os.path.join(source_folder, file_name)
        shutil.move(file_path, folder_path)
        
        # 增加文件数量
        file_dict[new_file_name][1] += 1
    else:
        # 文件名不存在，创建新文件夹，并将文件移动到新文件夹
        folder_name = new_file_name.replace('-', '_')
        folder_path = os.path.join(target_folder, folder_name)
        os.makedirs(folder_path, exist_ok=True)
        
        file_path = os.path.join(source_folder, file_name)
        shutil.move(file_path, folder_path)
        
        # 添加文件夹路径和文件数量到字典
        file_dict[new_file_name] = [folder_path, 1]

# 写入CSV文件
with open(csv_file, 'w', newline='') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(['文件名', '文件夹', '文件数量'])
    
    for file_name, folder_info in file_dict.items():
        folder_path, file_count = folder_info
        writer.writerow([file_name, folder_path, file_count])

print("程序执行完毕。")
