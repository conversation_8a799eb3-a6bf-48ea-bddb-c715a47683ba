import re
import os
from datetime import datetime

filename = 'D:/works/gzh/uu.txt'
with open(filename, 'r', encoding='utf-8') as f:
    contents = f.read()

pattern = r'## 必应\n(.*?)\n## 用户'
bing_contents = re.findall(pattern, contents, re.S)

# for item in bing_contents:
#     print(item)

# for i, item in enumerate(bing_contents):
#    print(f"{i+1}.{item}")

   # 重命名文件
now = datetime.now()  
# filename = 'uu.txt'
new_filename = f"{os.path.splitext(filename)[0]}_{now.strftime('%Y%m%d%H%M%S')}{os.path.splitext(filename)[1]}"
os.rename(filename, new_filename)