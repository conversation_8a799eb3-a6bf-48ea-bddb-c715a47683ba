import csv
import datetime

# 读取CSV文件
with open('../input/title_id_update.csv', 'r', encoding='utf-8') as csvfile:
    reader = csv.reader(csvfile)
    data = list(reader)

# 生成HTML表格的函数
def generate_table_html(data):
    table_html = '<table class="my-table">\n'
    table_html += '<tr><th>Title</th></tr>\n'
    for i, row in enumerate(data):
        num = i + 1
        title = row[0]
        id = row[1]

        link = f'<a href="https://test/{id}" target="_blank">{title}</a>'
        table_html += f'<tr><td>{link}</td></tr>\n'
    table_html += '</table>'
    return table_html

# 定义初始数据和搜索结果数据
initial_data = data
search_results = []

# 定义CSS样式
css = '''
.my-table {
    border-collapse: collapse;
    border: 1px solid #ddd;
    margin: 0 auto;
}
.my-table th, .my-table td {
    border: 1px solid #ddd;
    padding: 5px;
    text-align: center;
    vertical-align: middle;
}
a {
  color: black;
}

a:visited {
  color: gray;
}
'''

# 生成初始HTML表格
table_html = generate_table_html(initial_data)

# 生成搜索表单的HTML代码
search_form_html = '''
<form id="search-form" action="" onsubmit="searchTable(event)">
    <input type="text" id="search-input" name="search" placeholder="输入关键字">
    <input type="submit" value="搜索">
</form>
'''

# 生成返回按钮的HTML代码
return_button_html = '''
<button onclick="window.history.back()">返回</button>
'''

# 定义完整的HTML代码
html_content = f'''
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <style>{css}</style>
    <script>
        function searchTable(event) {{
            event.preventDefault();
            var input = document.getElementById('search-input');
            var filter = input.value.toLowerCase();
            var table = document.getElementById('my-table');
            var rows = table.getElementsByTagName('tr');

            search_results = [];
            for (var i = 1; i < rows.length; i++) {{
                var title = rows[i].getElementsByTagName('td')[0].innerText.toLowerCase();
                if (title.includes(filter)) {{
                    search_results.push(rows[i]);
                    rows[i].style.display = '';
                }} else {{
                    rows[i].style.display = 'none';
                }}
            }}
        }}

        function resetTable() {{
            var table = document.getElementById('my-table');
            var rows = table.getElementsByTagName('tr');
            for (var i = 1; i < rows.length; i++) {{
                rows[i].style.display = '';
            }}
        }}
    </script>
</head>
<body>
    {search_form_html}
    {return_button_html}
    {table_html}
</body>
</html>
'''

# 将HTML写入文件
with open('../output/sjjh.html', 'w', encoding='utf-8') as htmlfile:
    htmlfile.write(html_content)
