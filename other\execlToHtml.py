import openpyxl

def convert_xlsx_to_html(xlsx_file, output_file):
    # 打开Excel文件
    wb = openpyxl.load_workbook(xlsx_file)
    sheet = wb.active

    # 获取表格的行数和列数
    max_row = sheet.max_row
    max_column = sheet.max_column

    # 创建HTML表格
    html_table = "<table>"

    # 遍历每个单元格
    for row in range(1, max_row + 1):
        html_table += "<tr>"
        for column in range(1, max_column + 1):
            cell = sheet.cell(row=row, column=column)

            # 获取单元格的值和缩进级别
            cell_value = cell.value
            indent_level = cell.alignment.indent

            # 添加缩进和单元格内容到HTML表格中
            html_table += f"<td style='padding-left: {indent_level * 5}px;'>{cell_value}</td>"
        html_table += "</tr>"

    # 关闭HTML表格标签
    html_table += "</table>"

    # 将HTML表格保存到文件
    with open(output_file, "w") as file:
        file.write(html_table)

# 示例用法
xlsx_file = "../input/test.xlsx"
output_file = "../input/output1111.html"
convert_xlsx_to_html(xlsx_file, output_file)
