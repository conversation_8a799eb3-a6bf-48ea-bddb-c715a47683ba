from aip import AipNlp
import pandas as pd

# 百度翻译API的配置信息
APP_ID = ''
API_KEY = ''
SECRET_KEY = ''

# 初始化百度翻译API的客户端
client = AipNlp(APP_ID, API_KEY, SECRET_KEY)

# 读取CSV文件
df = pd.read_csv('../input/file_organization.csv')

# 将-替换为空格
df['Folder Name'] = df['Folder Name'].str.replace('-', ' ')

# 定义翻译函数
def translate_text(text):
    # 调用百度翻译API进行翻译
    result = client.translate(text, 'en', 'zh')
    if 'trans_result' in result and len(result['trans_result']) > 0:
        return result['trans_result'][0]['dst']
    return ''

# 翻译为中文
df['Folder Name'] = df['Folder Name'].apply(translate_text)

# 输出结果
print(df)
