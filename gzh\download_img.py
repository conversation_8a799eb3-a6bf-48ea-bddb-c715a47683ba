

import re
import requests
from env import GlobalVars



link_pattern = r'\[download 4k\]\((https://[^)]+)\)'

def doowload_moth_img(month_readme_file,month_file):
    with month_readme_file.open('r', encoding='utf-8') as f:
            # 读取文件内容
        markdown_content = f.read()

    # 这里是从你提供的文件内容中读取的示例

    # 使用正则表达式来查找所有下载链接
    # 假设链接是以 'download 4k' 文本开始的，并且紧跟着一个竖线 '|'，然后是一个URL
    # 正则表达式会查找所有匹配的模式 
    links = re.findall(link_pattern, markdown_content)
    # unique_links = list(set(links))

    month_dwonload_folder_path=GlobalVars.base_jingju_path.joinpath('img').joinpath(month_file)
    # 创建一个目录来保存下载的图片
    month_dwonload_folder_path.mkdir(parents=True, exist_ok=True)

    # 遍历所有找到的链接
    for index, link in enumerate(links, start=1):
        # 清理链接，去除可能的空格和换行符
        link = link.strip()
        # 检查链接是否有效
            # 构建完整的URL（这里假设链接已经是完整的，如果需要可以添加基础URL）
            # 发起请求下载图片
        response = requests.get(link, stream=True)
            # 检查请求是否成功
        if response.status_code == 200:
                # 为下载的图片创建一个文件名
                img_name=f'image_{index}.jpg'
                img_path=month_dwonload_folder_path.joinpath(img_name)
                # 打开文件并写入响应内容
                with img_path.open ('wb') as f:
                    f.write(response.content)
                print(f'download ok: {img_name}')
        else:
                print(f'download fail: {img_name}')


def main():
    GlobalVars.set_system_path()
    for entry in GlobalVars.base_bing_path.iterdir():
        month_readme_file=entry.joinpath("README.md")
        doowload_moth_img(month_readme_file,entry.name)
     

if __name__ == "__main__":
    main()