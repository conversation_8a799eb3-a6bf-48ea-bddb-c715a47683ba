import csv
from urllib.parse import quote_plus
from datetime import datetime
currentDateAndTime = datetime.now()
currentTime = currentDateAndTime.strftime("%Y%m%d")

input_file = 'D:/works/input/wb_hot_title_'+currentTime+'.csv'
output_file =  'D:/works/input/wb_url_'+currentTime+'.csv'
fixed_string = 'https://m.weibo.cn/search?containerid=100103type=1&q='

with open(input_file, 'r',encoding='utf-8') as input_file:
    reader = csv.reader(input_file)
    rows = list(reader)

with open(output_file, 'w',encoding='utf-8') as output_file:    
    writer = csv.writer(output_file)
    for row in rows:
        encoded_row = [fixed_string + quote_plus(item) for item in row]
        writer.writerow(encoded_row)