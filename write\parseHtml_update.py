import csv
import datetime

# 读取CSV文件
with open('../gzh/街角的洋果果/街角的洋果果.csv', 'r', encoding='utf-8') as csvfile:
    reader = csv.reader(csvfile)
    data = list(reader)

# 生成HTML表格
table_html = '<table class="my-table" id="myTable">\n'
table_html += '<tr><th>序号</th><th>日期</th><th>标题</th></tr>\n'
for i, row in enumerate(data):
    num = i + 1
    date,title = row[0].split('_街角的洋果果_')
    link_url = row[1]
    link = f'<a href="{link_url}" target="_blank">{title}</a>'
    table_html += f'<tr><td>{num}</td><td>{date}</td><td>{link}</td></tr>\n'
table_html += '</table>'

div_html='<div class="center"><input type="text" id="filterInput" onkeyup="filterTable()" class="filter-input" /><span id="rowCountLabel" class="row-count-label"></span></div>'

# 定义CSS样式
css = '''
.my-table {
    border-collapse: collapse;
    border: 1px solid #ddd;
    margin: 0 auto;
    width: 1300px;
}
.my-table th, .my-table td {
    border: 1px solid #ddd;
    padding: 5px;
    text-align: center;
    vertical-align: middle;
}
a {
  color: black;
}

a:visited {
  color: gray;
}

.center {
      text-align: center;
      margin: 0 auto;
      display: block;
    }


.filter-input {

  margin-bottom: 10px;
  width: 300px;
  height: 30px;
  font-size: 16px;
}

.row-count-label {
      margin-left: 15px;
    }
'''

script='''
function filterTable() {
    var input, filter, table, tr, td, i, j, txtValue;
    input = document.getElementById("filterInput");
    filter = input.value.toUpperCase();
    table = document.getElementById("myTable");
    tr = table.getElementsByTagName("tr");

    var rowCount = 0; // 用于计算符合条件的行数

    for (i = 1; i < tr.length; i++) {
      tds = tr[i].getElementsByTagName("td");
      var matchFound = false;
      for (j = 0; j < tds.length; j++) {
        td = tds[j];
        if (td) {
          txtValue = td.textContent || td.innerText;
          if (txtValue.toUpperCase().indexOf(filter) > -1) {
            matchFound = true;
            break;
          }
        }
      }
      if (matchFound) {
        tr[i].style.display = "";
        rowCount++;
      } else {
        tr[i].style.display = "none";
      }
    }

    var rowCountLabel = document.getElementById("rowCountLabel");
    rowCountLabel.textContent = "行数: " + rowCount; // 显示行数
  }
'''

# 将HTML和CSS写入文件
with open('../gzh/街角的洋果果.html', 'w', encoding='utf-8') as htmlfile:
    htmlfile.write(f'<html><head><style>{css}</style></head><script>{script}</script><body>{div_html}{table_html}</body></html>')

