import re

file_path = r"C:\Users\<USER>\Desktop\szjcId\cg1\cg2-3.txt"
file_path.encode('utf-8')

with open(file_path) as f:
    content = f.read()

pattern = r'(\d+-\d+):(.+),' 
data = {}
for line in content.split('\n'):
    line= re.sub(pattern, r'\1:\2', line)
    match = re.match(r'(\d+-\d+):(.*)', line)
    if match:
        key = match.group(1)
        values = [x.strip() for x in match.group(2).split(',')]
        data[key] = values


# key_groups = [['1515-27','1516-27','1517-27'], ['1512-27','1513-27','1514-27','1512-2701'],
#               ['1416-2701','1510-27','1511-27','1509-27'],['1416-27','1417-27','1418-27'],
#               ['1413-27','1414-27','1415-27'],['1413-2701','1412-27','1327-27','1328-27'],['1322-27','1323-27','1324-27'],
#               ['1322-2701','1325-27','1326-27']]

# key_groups = [['1316-25','1317-23','1318-23'], ['1319-27','1320-27','1321-27','1316-2701'],
#               ['1502-27','1504-24','1505-27'],['1502-2701','1508-2709','1503-27','1411-27'],
#               ['1501-24','1409-27','1410-27']]


# key_groups = [['1406-24','1407-27','1408-2702'], ['1408-2701','1405-27','1403-2702'],
#               ['1401-24','1402-27','1312-2702'],['1312-2701','1311-27','1310-24'],
#               ['1226-2702','1302-24','1303-25'],['1306-2502','1304-24','1305-2501'],
#               ['1305-2502','1307-27','1308-27','1309-24']]

key_groups = [['1408-2701','1405-27','1403-2702','1404-27']]
# key_groups = [['2101-25','2102-24','2103-24','2104-24','2105-24'],
#               ['2106-24','2107-24','2108-24','2109-24''2110-25'],
#               ['2111-25','2112-25','2113-25','2114-26'],
#               ['2115-25','2116-25','2117-25','2118-25'],
#               ['2119-25','2120-25','2121-25','2122-25']
#               ['2111-25','2112-25','2113-25','2114-26']
#             ]

for j,key_group in enumerate(key_groups):
    content = ''
    for key in key_group:
       values = data[key]
       
       content += f'//{key}\n'
       half_len=len(values)//2
       for i, value in enumerate(values):
            if i < half_len:
                content += f'{value} = [up{i+1}]\n'
            else:
                content +=f'{value} = [down{i-half_len+1}]\n'
       content += f'\n'
    # 写入文件
  # fname =r'C:\Users\<USER>\Desktop\szjcId\cg1\szjc_relay_f00003d'+str(j+34)+'_('+'_'.join(key_group) + ').txt' 

    fname =r'C:\Users\<USER>\Desktop\szjcId\cg1\szjc_relay_f0000328'+'_('+'_'.join(key_group) + ').txt' 
    # fname.decode('utf-8')
    with open(fname,'w',encoding="utf-8") as f:
       f.write(content)