import math
import datetime

def calcShadowLength(lat, lon, h, dt):

  # 计算太阳高度角
  declination = 23.45 * math.sin(math.radians(360 / 365 * (dt.timetuple().tm_yday - 81)))
  eqtime = 229.18 * (0.000075 + 0.001868 * math.cos(math.radians(dt.timetuple().tm_yday - 81)) - math.sin(math.radians(dt.timetuple().tm_yday - 81)) / 0.01446)
  time_offset = eqtime - 4 * lon + 60 * dt.timetuple().tm_min + dt.timetuple().tm_hour * 15   
  ts = math.degrees(math.acos(math.sin(math.radians(declination)) * math.sin(math.radians(lat)) + math.cos(math.radians(declination)) * math.cos(math.radians(lat)) * math.cos(math.radians(dt.timetuple().tm_hour * 15 + time_offset))))
#   print(ts)
  if ts > 90:
   ts = 180 - ts
  # 计算影长
  shadow_len = h / math.tan(math.radians(ts))

  return shadow_len

# if __name__ == '__main__':
  
#   lat = 33.121294
#   lon = 120.834475
#   h = 100
#   dt = datetime.datetime(2023, 9, 15, 16, 0)

#   print(calcShadowLength(lat, lon, h, dt))

if __name__ == '__main__':

  lat, lon = 33.121294, 120.834475 
  h = 100

  start = datetime.datetime(2023, 12, 15, 8, 0)
  end = datetime.datetime(2023, 12, 15, 9, 0)
  delta = datetime.timedelta(minutes=15)

  print("时间\t影长")

  dt = start
  while dt <= end:
    shadow_len = calcShadowLength(lat, lon, h, dt)
    print(f"{dt.time().strftime('%H:%M')}\t{shadow_len:.2f}")
    dt += delta