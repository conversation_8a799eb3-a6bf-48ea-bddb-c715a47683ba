import re
import os
import shutil
import json
from pathlib import Path
from playwright.sync_api import <PERSON><PERSON>, sync_playwright, expect
from env import GlobalVars


def copy_chrome_cookies():
    """复制Chrome浏览器的Cookie到自定义用户目录"""
    try:
        # 源Cookie文件路径
        source_cookie_path = os.path.expanduser("~/Library/Application Support/Google/Chrome/Default/Cookies")
        source_login_data = os.path.expanduser("~/Library/Application Support/Google/Chrome/Default/Login Data")
        
        # 目标目录
        target_dir = os.path.join(GlobalVars.chrome_userdata_path, "Default")
        os.makedirs(target_dir, exist_ok=True)
        
        # 复制Cookie文件
        if os.path.exists(source_cookie_path):
            shutil.copy2(source_cookie_path, os.path.join(target_dir, "Cookies"))
            print(f"已复制Cookie文件到: {target_dir}")
        
        # 复制登录数据
        if os.path.exists(source_login_data):
            shutil.copy2(source_login_data, os.path.join(target_dir, "Login Data"))
            print(f"已复制登录数据到: {target_dir}")
            
        # 创建基本的首选项文件
        preferences_file = os.path.join(target_dir, "Preferences")
        if not os.path.exists(preferences_file):
            with open(preferences_file, 'w') as f:
                json.dump({
                    "profile": {
                        "exit_type": "Normal",
                        "exited_cleanly": True
                    }
                }, f)
        
        return True
    except Exception as e:
        print(f"复制Cookie失败: {e}")
        return False


def run(playwright: Playwright) -> None:
    # 使用正确的用户数据目录路径启动浏览器
    try:
        # 首先尝试复制Chrome的Cookie
        # copy_chrome_cookies()
        
        # 启动浏览器
        browser = playwright.chromium.launch_persistent_context(
            user_data_dir=GlobalVars.chrome_userdata_path,
            channel="chrome", 
            headless=False,
            ignore_default_args=["--enable-automation"],
            args=[
                "--disable-blink-features=AutomationControlled",
                "--no-sandbox"
            ]
        )

        page1 = browser.new_page()
        page1.goto("https://mp.toutiao.com/profile_v4/index")
        print("已加载页面，检查是否已登录")
        page1.wait_for_timeout(3000)  # 等待页面加载完成
        
        # 检查是否需要点击"文章"链接（如果已登录才会出现）
        if page1.get_by_role("link", name="文章").is_visible():
            print("已检测到登录状态，继续执行")
            page1.get_by_role("link", name="文章").click()
            page1.wait_for_timeout(5000)
            # page1.locator(".byte-modal-close-icon").click()
            # page1.locator(".byte-drawer-mask").click()
            page1.get_by_placeholder("请输入文章标题（2～30个字）").fill("你好")
            page1.wait_for_timeout(1000)
            page1.locator("div").filter(has_text=re.compile(r"^请输入正文$")).fill("你好1111")

            # 创作助手按钮
            # get_by_role("heading", name="头条创作助手").locator("svg")
            # 文章创作添加封面
            # locator(".article-cover-add")

            # 微头条创作助手收起图标
            # locator(".icon-wrap > .byte-icon")
            # 微头条添加图片按钮
            # get_by_role("button", name="图片")
            
            # page1.keyboard.press('Ctrl+V')

            page1.locator(".add-icon").click()
            page1.get_by_role("button", name="本地上传").get_by_role("textbox").click()
            page1.get_by_role("button", name="本地上传").get_by_role("textbox").set_input_files(["10001.webp", "10003.png"])
            page1.get_by_role("button", name="确定").click()
            page1.locator("g > circle").first.click()
        else:
            print("未检测到登录状态，请先在Chrome浏览器中登录头条账号")
            print("尝试手动登录...")
            # 等待用户手动登录
            input('请在打开的浏览器中完成登录，然后按Enter继续...')
            
        input('Press Enter to close the browser...')
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        if 'browser' in locals():
            browser.close()


with sync_playwright() as playwright:
    GlobalVars.set_system_path()
    run(playwright)
