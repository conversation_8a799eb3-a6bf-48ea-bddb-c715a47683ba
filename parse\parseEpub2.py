import pandas as pd
from ebooklib import epub

# 读取CSV文件
df = pd.read_csv('input.csv')

# 创建EPUB电子书对象
book = epub.EpubBook()

# 设置书名、作者等元数据
book.set_title('My EBook')
book.set_author('Your Name')

# 创建目录
toc = []

# 遍历CSV文件的每一行
for index, row in df.iterrows():
    title = row['title']
    id = row['id']

    # 创建章节对象
    chapter = epub.EpubHtml(title=title, file_name=f'chapter_{index}.xhtml', lang='en')

    # 生成标题的超链接
    content = f'<h1>{title}</h1><p><a href="{id}_link">{title}</a></p>'
    chapter.content = f'<html><head></head><body>{content}</body></html>'

    # 添加章节到电子书
    book.add_item(chapter)

    # 添加目录项
    toc.append(epub.Section(title, [chapter]))
    chapter.toc = True

# 设置目录
book.toc = tuple(toc)

# 添加默认样式文件
book.add_item(epub.EpubNcx())
book.add_item(epub.EpubNav())

# 将章节添加到脊柱中
for chapter in book.get_items_of_type(epub.EpubHtml):
    book.add_spine(chapter)

# 生成EPUB文件
epub.write_epub('output.epub', book, {})

print('EPUB文件生成成功！')
