import pandas as pd
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch

# 读取csv文件并存储为pandas dataframe
df = pd.read_csv('../input/title_id_update.csv')

# 创建一个PDF文档并设置页面大小
pdf = canvas.Canvas('../output/jh.pdf', pagesize=(8.5*inch, 11*inch))

font_size = 14
# 设置字体大小和样式
pdf.setFont("Helvetica", font_size)

# 遍历每一行并创建超链接
for index, row in df.iterrows():
    title = row[0]
    id = row[1]
    url = f'https://wx.zsxq.com/dweb2/index/topic_detail/{id}'
    x = 1 * inch  # 左下角x坐标
    y = (10 - index) * inch  # 左下角y坐标
    width = pdf.stringWidth(title)  # 根据文本长度计算矩形的宽度
    height = font_size  # 获取字体的高度

    # 绘制文本和超链接
    pdf.drawString(x, y, title)
    # pdf.linkURL(url, x, y, x + width, y + height)

# 保存PDF文档并关闭canvas
pdf.save()
