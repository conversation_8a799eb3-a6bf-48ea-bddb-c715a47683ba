import ephem
import math
import datetime
import pytz

local_tz = pytz.timezone('Asia/Shanghai')
# 创建观测地点
obs = ephem.Observer()
# 纬度
obs.lat = '33.121294'   
# 经度
obs.lon = '120.834475'
# obs.elevation = 100

# 计算的时间范围
start = datetime.datetime(2023, 12, 15, 8, 0, 0)
end = datetime.datetime(2023, 12, 15, 16, 0, 0)
step = datetime.timedelta(minutes=15)
sun = ephem.Sun()
r = 100

# 循环计算每个时间点
cur = start
while cur <= end:
    # obs.date = str(cur)
    # lt = ephem.localtime(obs.date)
    # print(lt)
    local_dt = local_tz.localize(cur)
    utc_dt = local_dt.astimezone(pytz.utc)

    # print(cur,utc_dt)
    obs.date=utc_dt
    # 计算太阳位置和影子长度
    sun.compute(obs)
    deg = math.degrees(sun.alt)

    # sun.az  获得太阳的方位角
    
    print(cur.strftime("%H:%M"),round(deg,2), round(r / math.tan(sun.alt),2))
    
    cur += step