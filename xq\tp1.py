import csv

def parse_csv(filename):
    building_rooms = {}
    building_duplicates = {}

    with open(filename, newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        next(reader)  # 跳过标题行（如果存在）
        
        for row in reader:
            building = int(row[15])  # 假设P列是第16列
            room = int(row[17])      # 假设R列是第18列
            
            if building not in building_rooms:
                building_rooms[building] = []
                building_duplicates[building] = set()
            
            building_rooms[building].append(room)
            
            # 检查房间号是否已经存在，并记录重复项
            if room in building_rooms[building]:
                building_duplicates[building].add(room)
            
    # 对每个楼栋号的房间号列表进行排序并去除重复
    for building in building_rooms:
        building_rooms[building].sort()
        unique_rooms = list(set(building_rooms[building]))
        building_rooms[building] = unique_rooms

    return building_rooms, building_duplicates

filename = r"C:\Users\<USER>\Desktop\14501905_202405161950335531.csv"
building_rooms, building_duplicates = parse_csv(filename)

for building, rooms in sorted(building_rooms.items()):
    print(f"Building {building}: Sorted unique rooms - {rooms}")
    if building in building_duplicates and building_duplicates[building]:
        print(f"  Duplicates found: {sorted(building_duplicates[building])}")