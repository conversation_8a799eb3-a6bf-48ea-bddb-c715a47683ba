import openpyxl

wb = openpyxl.load_workbook('../input/wy66666.xlsx')

# print(wb.sheetnames)

arrays = [
    ['101', '102', '201'], 
    ['101', '203', '302'],
    ['103', '201', '202']
]

for i, array in enumerate(arrays):
    ws = wb[f'{i+1}']
    
    for cell in ws['A1:Z100']:
        # if isinstance(cell, tuple):
        #     continue 
        print(cell.value)
        if cell.value in array:
            print("ok")
            cell.fill = openpyxl.styles.PatternFill(fgColor='green')

wb.save('../input/wy66666.xlsx')