import json
import csv

with open('../xhs/xhs_list_new.txt',encoding='utf-8') as f:
    data = json.load(f)

items = data['data']['items']

with open('../xhs/output.csv', 'w', newline='',encoding='utf-8') as f:
    writer = csv.writer(f)
    writer.writerow(['display_title', 'id', 'nick_name', 'user_id', 'liked_count', 'trace_id']) 
    for item in items:
        note_card = item['note_card']
        writer.writerow([
            note_card['display_title'],
            item['id'],
            note_card['user']['nick_name'],
            note_card['user']['user_id'],
            note_card['interact_info']['liked_count'],
            note_card['trace_id']
        ])