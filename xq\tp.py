import csv

def parse_csv(filename):
    # 初始化一个字典来存储楼栋号和对应的房间号列表
    building_rooms = {}

    
    # 打开CSV文件进行读取
    with open(filename, newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        next(reader)  # 跳过标题行（如果存在）
        
        # 遍历CSV文件的每一行
        for row in reader:
            building = int(row[17])  # 假设P列是第16列，索引为15
            room = int(row[19])      # 假设R列是第18列，索引为17
            
            # 如果楼栋号不在字典中，添加一个新的列表
            if building not in building_rooms:
                building_rooms[building] = []
            
            # 将房间号添加到对应楼栋号的列表中
            building_rooms[building].append(room)
    
    # 对每个楼栋号的房间号列表进行排序
    for building in building_rooms:
        building_rooms[building].sort()
    
    return building_rooms

# 假设CSV文件名为'buildings.csv'
# filename = r"C:\Users\<USER>\Desktop\14501905_202405161950335531.csv"
old_filename = r"C:\Users\<USER>\Desktop\14501905_202405201755251143.csv"
old_building_rooms = parse_csv(old_filename)

# 14501905_202405210905469747.csv

filename = r"C:\Users\<USER>\Desktop\14501905_202405210905469747.csv"
building_rooms = parse_csv(filename)


# real_bu=1
# diff_len=0
# for building, rooms in sorted(building_rooms.items()):
#     # print(f"Building {building}: {rooms}")
#     if(building<4):
#      real_bu=building
#     elif(building>=4 and building<=11):
#      real_bu=building+1
#     elif(building>11):
#      real_bu=building+3

#     unique_to_rooms = list(set(rooms)-set(old_building_rooms[building]))
#     diff_len=diff_len+len(unique_to_rooms)
#     print(f"buAdd {real_bu}")
#     for item in unique_to_rooms:
#       print(item)
# print(f"newAdd {diff_len}")

# 打印结果
total_len=0
for building, rooms in sorted(building_rooms.items()):
    # print(f"Building {building}: {rooms}")
    if(building<4):
     print(f"bu {building}")
    elif(building>=4 and building<=11):
     print(f"bu {building+1}")
    elif(building>11):
     print(f"bu {building+3}")
    ok_rorms=list(dict.fromkeys(rooms))
    total_len=total_len+len(ok_rorms)
    for item in ok_rorms:
      print(item)

print(f"total_len {total_len}")

v119 = list(range(101, 109))
v219 = list(range(201, 209))
v319 = list(range(301, 309))
v419 = list(range(401, 409))
v519 = list(range(501, 509))
v619 = list(range(601, 609))
v719 = list(range(701, 709))
v819 = list(range(801, 809))



ok_bu={'1':v219+v319+v419+v519+v619+v719[1:8]+v819[1:8],
     '2':v119[:5]+v219[:5]+v319[:5]+v419[:5]+v519[:5]+v619[:5]+v719[:5]+v819[:5],
     '3':v119[:4]+v219[:4]+v319[:4]+v419[:4]+v519[:4]+v619[:4]+v719[1:4]+v819[1:4],
     '5':v119[:4]+v219[:4]+v319[:4]+v419[:4]+v519[:4]+v619[:4]+v719[:4]+v819[:4],
     '6':v119[:5]+v219[:5]+v319[:5]+v419[:5]+v519[:5]+v619[:5]+v719[1:5]+v819[1:3],
     '7':v119[:5]+v219[:5]+v319[:5]+v419[:5]+v519[:5]+v619[:5]+v719[:5]+v819[2:5],
     '8':v119[:4]+v219[:4]+v319[:4]+v419[:4]+v519[:4]+v619[:4]+v719[1:4]+v819[1:4],
     '9':v119[:7]+v219[:7]+v319[:7]+v419[:7]+v519[:7]+v619[:7]+v719[1:8]+v819[1:6]+v819[7:8],
     '10':v119[:8]+v219[:8]+v319[:8]+v419[:8]+v519[:8]+v619[:8]+v719[1:8]+v819[1:2]+v819[3:6]+v819[7:8],
     '11':v119[:2]+v219[:2]+v319[:2]+v419[:2]+v519[:2]+v619[:2]+v719[:2],
     '12':v119[:4]+v219[:4]+v319[:4]+v419[:4]+v519[:4]+v619[:4]+v719[1:4]+v819[1:4],
     '15':v119[:5]+v219[:5]+v319[:5]+v419[:5]+v519[:5]+v619[:5]+v719[:5]+v819[:5],
     '16':v119[:4]+v219[:4]+v319[:4]+v419[:4]+v519[:4]+v619[:4]+v719[1:4]+v819[1:4],
     '17':v119[:5]+v219[:5]+v319[:5]+v419[:5]+v519[:5]+v619[:5]+v719[:5]+v819[:5],
     '18':v119[:4]+v219[:4]+v319[:4]+v419[:4]+v519[:4]+v619[:4]+v719[1:4]+v819[1:4],
    '19':v219[:7]+v319[:7]+v419[:7]+v519[:7]+v619[:7]+v719[1:7]+v819[1:2]+v819[3:7]
}


real_bu=1
for building, rooms in sorted(building_rooms.items()):
    # print(f"Building {building}: {rooms}")
    if(building<4):
     real_bu=building
    elif(building>=4 and building<=11):
     real_bu=building+1
    elif(building>11):
     real_bu=building+3

    unique_to_rooms = list(set(rooms) - set(ok_bu[str(real_bu)]))
    print(f"bu {real_bu} {unique_to_rooms}")




    # print(f"Building {building}: {list(dict.fromkeys(rooms))}")