import csv
import os
path = "D:/bill2"
files= os.listdir(path)
sum=0
for file in files:
  print(file)
  with open(path+"/"+file,'r') as csvfile:
    total=0
    num=0
    reader = csv.reader(csvfile)
    for i,rows in enumerate(reader):
        if i >= 531 and i <=554:
          row = rows
          print(row)
          num=num+1
          total+=float(row[2])
    print(str(file)+" : "+str(num))
    print(str(file)+" : "+str(total))
    sum+=total
    if num == 24:
      print("ok")
   
	
    
print("sum: "+str(sum))