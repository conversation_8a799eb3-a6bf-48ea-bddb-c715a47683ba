import os
import hashlib
import poimage

os.path.sep = '/'
# 指定要处理的文件夹路径  
folder_path = 'D:/works/gzh/ImageAssistant/blog/'

# 获取指定路径下的原文件夹列表
folder_names = os.listdir(folder_path) 

# 生成英文文件夹名  
# eng_names = []
# for name in folder_names:
#   m = hashlib.md5()
#   m.update(name.encode('utf-8'))
#   eng_names.append(m.hexdigest())

# # 重命名文件夹  
# for i in range(len(folder_names)):

#   os.rename(os.path.join(folder_path, folder_names[i]), os.path.join(folder_path, eng_names[i]))

# 处理每个文件夹内文件
for folder in folder_names:
  folder_path = os.path.join(folder_path, folder)
  print(folder_path)
  files = os.listdir(folder_path)
  for file in files:
    # 处理文件操作
    file_path = os.path.join(folder_path, file)
    print(file_path)
    file_path = file_path.replace('\\', '/')
    name = os.path.splitext(file)[0]
    print(file_path,name)

    poimage.del_watermark(
    input_image=file_path,
    output_image=folder_path+name+"_new.jpg")

# 恢复原文件夹名
# for i in range(len(eng_names)):
#   os.rename(os.path.join(folder_path, eng_names[i]), os.path.join(folder_path, folder_names[i]))

print('Done!')