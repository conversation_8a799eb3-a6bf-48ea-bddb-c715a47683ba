from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from env import GlobalVars 
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
import time
from selenium.webdriver.common.keys import Keys


def set_browser():
    service = Service(executable_path=GlobalVars.chromedriver_path)

    # 创建Chrome选项对象
    options = Options()

    # 设置Chrome选项，例如启动时最大化窗口
    options.add_argument("--start-maximized")
    options.add_argument(GlobalVars.chrome_userdata_path)

    # 程序结束后，不关闭浏览器
    options.add_experimental_option('detach', True)
    # 设置无头模式
    options.add_argument('--headless=new') 
     # 禁用gpu加速
    # options.add_argument('--disable-gpu') 

    # 可以添加更多的选项，例如禁用图片来加速加载
    # options.add_argument("--disable-images")

    # 创建WebDriver实例
    browser = webdriver.Chrome(service=service, options=options)
    return browser

def open_browser(browser):
    browser.get("https://www.doubao.com/")
    # browser.execute_script('window.open("https://www.tiangong.cn/")')
    # browser.execute_script('window.open("https://metaso.cn/")')
    browser.execute_script('window.open("https://kimi.moonshot.cn/")')
    # browser.execute_script('window.open("https://cp.baidu.com/")')
   
    return browser


def handle_browser(write_str,browser):
    input_kw=None
    current_windows = browser.window_handles
    for tab in current_windows:
        browser.switch_to.window(tab)
        if 'doubao' in browser.current_url:
            # CSS_SELECTOR   使用元素的自定义属性来定位。例如，一个元素可能有一个`data-testid`属性。
            input_kw=browser.find_element(By.CSS_SELECTOR, '[data-testid="chat_input_input"]')
            input_kw.send_keys(Keys.ENTER)
        if 'kimi' in browser.current_url:
            input_kw=browser.find_element(By.CLASS_NAME, r"editorContentEditable___FZJd9")
            # 橙篇没有获得焦点时，生成会停止，需要单独处理一下，现在无头模式只能生成第一篇
        # if 'cp' in browser.current_url:
        #     input_kw=browser.find_element(By.CLASS_NAME, r"content")
        if input_kw is not None:
          input_kw.send_keys(write_str)
          time.sleep(3)
          input_kw.send_keys(Keys.ENTER)
          input_kw=None
          time.sleep(15)


def write_wb_ar(browser):
    content=GlobalVars.get_file_content(GlobalVars.wb_template_path)
    data_content=GlobalVars.get_file_content(GlobalVars.wb_data_path)
    data_lines=data_content.split('\n')
    for line in data_lines:
        if 'https' not in line:
            write_str=content.replace('某某',line)
            print(line)
            handle_browser(write_str,browser)

def main():
    
   try: 
        GlobalVars.set_system_path()
        browser=set_browser()
        browser=open_browser(browser)
        time.sleep(6)
        write_wb_ar(browser)
        browser.quit()
   except Exception as e:
          print(e) 
          browser.quit()        



if __name__ == "__main__":
    main()