import re
from playwright.sync_api import Playwright, sync_playwright, expect
import time
from env import GlobalV<PERSON>



def run(playwright: Playwright) -> None:
    # 加上配置插件的功能
    browser = playwright.chromium.launch_persistent_context(user_data_dir=GlobalVars.chrome_userdata_path1,channel="chrome", headless=False)
    # context = browser.new_context()
    page = browser.new_page()
    page.goto("https://kimi.moonshot.cn/")
    page.keyboard.press('Alt+Q')

    # page.locator("#kw").click()
    # page.locator("#kw").fill("你好")
    # page.locator("#kw").press("Enter")
    # time.sleep(15)
    input('Press Enter to close the browser...')


    # ---------------------
    # context.close()
    # browser.close()


with sync_playwright() as playwright:
    GlobalVars.set_system_path()
    run(playwright)
