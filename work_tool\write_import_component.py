import re
import openpyxl

wb = openpyxl.Workbook()
sheet = wb.active

headers = ["生产商", "序列号", "型号", "组件坐标", "组件编号", "组串名称"]
sheet.append(headers)

value1 = 'ymx'  
value3 = 'v03'


file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\jg1.txt"
file_path.encode('utf-8')

with open(file_path) as f:
    content = f.read()

pattern = r'(\d+-\d+-\d+):(.+),' 
data = {}
key_groups = []
for line in content.split('\n'):
    line= re.sub(pattern, r'\1:\2', line)
    match = re.match(r'(\d+-\d+-\d+):(.*)', line)
    if match:
        key = match.group(1)
        values = [x.strip() for x in match.group(2).split(',')]
        data[key] = values
        key_groups.append(key)

spilt_group = {}
group_name_map = {}
for key in key_groups:
         # key 1-1101-28 name 1101-28  group_name 1101-28
         name=re.sub("^(\d+-)", "", key)
         group_name=name.split("-")[0]
         group_count=name.split("-")[1]
         if len(group_name)== 4: 
          name=name[1:]
          save_name=name[0]+"-"+name[1:]
          print(save_name)
         else:
             #先找一下，如果没有找到就存入完整的组串id：组串个数，找到了就取出求和，然后写入文件
            #  spilt_group[]
             real_group_name=group_name[:4]
             if(real_group_name in spilt_group):   
              real_group_count=int(group_count)+int(spilt_group[real_group_name])
            #   print(real_group_name)
            #   print(real_group_count)
              real_inpugt_name=real_group_name+"-"+str(real_group_count)
              real_inpugt_name=real_inpugt_name[1:]
              save_name=real_inpugt_name[0]+"-"+real_inpugt_name[1:]
              group_name_map[real_group_name]=save_name
            #   print(save_name)
             else:
                 spilt_group[real_group_name]=group_count

spilt_group = {}
for key, values in data.items():
         name=re.sub("^(\d+-)", "", key)
         group_name=name.split("-")[0]
        #  group_count=name.split("-")[1]
         if len(group_name)== 4: 
          name=name[1:]
          save_name=name[0]+"-"+name[1:]
        #  print(save_name)
         else:
           real_group_name_temp=group_name[:4]
           save_name= group_name_map[real_group_name_temp]
        #      #先找一下，如果没有找到就存入完整的组串id：组串个数，找到了就取出求和，然后写入文件
        #     #  spilt_group[]
        #      real_group_name=group_name[:4]
        #      if(real_group_name in spilt_group):   
        #       real_group_count=int(group_count)+int(spilt_group[real_group_name])
        #     #   print(real_group_name)
        #     #   print(real_group_count)
        #       real_inpugt_name=real_group_name+"-"+str(real_group_count)
        #       real_inpugt_name=real_inpugt_name[1:]
        #       save_name=real_inpugt_name[0]+"-"+real_inpugt_name[1:]
        #       print(save_name)
        #      else:
        #          spilt_group[real_group_name]=group_count

         for i, value in enumerate(values):
            value_ok=value.lstrip('0')
            row = [value1, value_ok, value3, value_ok, value, "NO"+save_name]  
            sheet.append(row)

wb.save(r'C:\Users\<USER>\Desktop\szjcId\jg2\jg1_component.xlsx')