import os
import shutil
import csv

# 源文件夹路径
source_folder = r'C:\Users\<USER>\Downloads\images8'

# CSV文件路径
csv_file = os.path.join(source_folder, 'output.csv')

# 固定的标题和标签
title = '美丽的大自然'
label = '#壁纸 #每日壁纸推荐'

# 获取源文件夹中所有文件
files = os.listdir(source_folder)

# 字典用于存储相同文件名的文件列表
file_dict = {}

# 遍历所有文件
for file_name in files:
    # 去掉最前面的数字和破折号
    new_file_name = file_name.split('-', 1)[-1]
    
    # 去除文件名中超过20个字符的部分
    new_file_name = new_file_name[:20]
    
    # 检查文件名是否已经在字典中
    if new_file_name in file_dict:
        # 文件名已存在，将文件移动到相应文件夹
        folder_path = file_dict[new_file_name]
        file_path = os.path.join(source_folder, file_name)
        shutil.move(file_path, folder_path)
    else:
        # 文件名不存在，创建新文件夹，并将文件移动到新文件夹
        # folder_name = new_file_name.replace('-', '_')
        folder_path = os.path.join(source_folder, new_file_name)
        os.makedirs(folder_path, exist_ok=True)
        
        file_path = os.path.join(source_folder, file_name)
        shutil.move(file_path, folder_path)
        
        # 添加文件夹路径到字典
        file_dict[new_file_name] = folder_path

# 写入CSV文件
with open(csv_file, 'w', newline='') as csvfile:
    writer = csv.writer(csvfile)
    # writer.writerow(['文件名',title, label])
    
    for file_name, folder_path in file_dict.items():
        writer.writerow([file_name])

# print("run ok")
