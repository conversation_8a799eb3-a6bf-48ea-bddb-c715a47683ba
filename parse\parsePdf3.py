import pandas as pd
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from reportlab.lib.utils import _simple_escape

def create_pdf_with_hyperlinks(csv_file, output_pdf, base_url):
    # 读取CSV文件
    df = pd.read_csv(csv_file)

    # 创建PDF文档
    doc = SimpleDocTemplate(output_pdf, pagesize=letter)

    # 设置样式
    styles = getSampleStyleSheet()
    style = styles["Normal"]

    # 添加段落
    content = []
    for index, row in df.iterrows():
        title = row[0]
        id = row[1]
        url = f"{base_url}{id}"
        hyperlink = f'<a href="{url}" color="blue">{_simple_escape(title)}</a>'
        content.append(Paragraph(hyperlink, style))
        content.append(Spacer(1, 12))

    # 生成PDF文件
    doc.build(content)

# 使用示例
csv_file = "../input/title_id_update.csv"
output_pdf = "../output/jhok.pdf"
base_url = "https://wx.zsxq.com/dweb2/index/topic_detail/"
create_pdf_with_hyperlinks(csv_file, output_pdf, base_url)
