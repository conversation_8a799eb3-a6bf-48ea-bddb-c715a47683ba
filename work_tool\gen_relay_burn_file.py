import re

with open('input.txt') as f:
    content = f.read()

data = {}
for line in content.split('\n'):
    match = re.match(r'(\w+):(.*)', line)
    if match:
        key = match.group(1)
        values = [x.strip() for x in match.group(2).split(',')]
        data[key] = values

with open('r_f_(aa-bb-cc-dd).txt', 'w') as f:
    for key, values in data.items():
        f.write(f'//{key}\n')
        half_len=len(values)//2
        for i, value in enumerate(values):
            if i < half_len:
                f.write(f'{value} = [up{i+1}]\n') 
            else:
                f.write(f'{value} = [down{i-half_len+1}]\n')
        f.write('\n')