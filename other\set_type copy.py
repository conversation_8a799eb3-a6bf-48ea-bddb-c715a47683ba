import re
import os
from datetime import datetime


now = datetime.now()
date_str = now.strftime("%Y%m%d")

filename = 'D:/works/gzh/Conversation.txt'
new_filename = f"{filename.split('.')[0]}_{date_str}.txt" 
with open(filename, 'r', encoding='utf-8') as f:
    contents = f.read()

# 过滤包含"微头条"或"搜索到的新闻"的行    
contents = re.sub(r'(?m)^.*(?:微头条|搜索到的新闻).*$', '', contents)

pattern = r'## 必应\n(.*?)\n## 用户'   
bing_contents = re.findall(pattern, contents, re.S)

pattern1 = re.compile(r'\n') 

# for item in bing_contents:
#     item = re.sub(pattern1, '', item)
#     print(f"{item}")

    


# for i, item in enumerate(bing_contents):
#     print(f"{i+1}.{item}")
# 每2句分段的正则表达式  
pattern = r'((?:[^！？。]{2,}[\！？。]{1}){1,2})'
# pattern =r'([\.\!\?。\!\?\n]){2}'

with open(new_filename, 'w', encoding='utf-8') as f:
  for i, item in enumerate(bing_contents):
    item = re.sub(pattern1, '', item)
    print(f"{item}")
    # 按句子分段
    parts = re.findall(pattern, item)
    
    # 打印每个部分 
    for j, part in enumerate(parts):
        # print(f"{i+1}-{j+1}.{part}")
        f.write(f"{part}\n")
    f.write("--------------\n")
    
now = datetime.now()
filename = 'Conversation(6).txt' 
new_filename1 = f"{os.path.splitext(filename)[0]}_old_{os.path.splitext(filename)[1]}"
os.rename(filename, new_filename1)