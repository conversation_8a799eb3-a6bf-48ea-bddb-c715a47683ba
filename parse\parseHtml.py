import csv

# 读取CSV文件
with open('title_id.csv', 'r',encoding='UTF-8') as csvfile:
    reader = csv.reader(csvfile)
    data = list(reader)

# 生成HTML表格
table_html = '<table>\n'
for row in data:
    title = row[0]
    id = row[1]
    link = f'<a href="https://wx.zsxq.com/dweb2/index/topic_detail/{id}" target="_blank">{title}</a>'
    table_html += f'<tr><td>{link}</td></tr>\n'
table_html += '</table>'

# 将HTML写入文件
with open('output.html', 'w',encoding='UTF-8') as htmlfile:
    htmlfile.write(table_html)
