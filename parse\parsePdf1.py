import pandas as pd
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib import colors

# 读取CSV文件
csv_file = '../input/title_id_update.csv'
df = pd.read_csv(csv_file,encoding='utf-8')

# 创建PDF文件
pdf_file = '../output/jh.pdf'
c = canvas.Canvas(pdf_file, pagesize=letter)

# 设置字体和大小
c.setFont("Helvetica", 12)

# 设置起始位置
x = 50
y = 750

# 特定的字符串
specific_string = "'https://wx.zsxq.com/dweb2/index/topic_detail/"

# 遍历CSV文件的每一行
for index, row in df.iterrows():
    title = row[0]
    id = row[1]
    url = specific_string + str(id)

    # 绘制文本
    c.drawString(x, y, title)

    # 添加超链接
    c.linkURL(url, (x, y - 4, x + c.stringWidth(title), y + 8), relative=1, thickness=1, color=colors.blue)

    # 更新y坐标以在下一行绘制文本
    y -= 20

    # 如果到达页面底部，创建新页面
    if y < 50:
        c.showPage()
        y = 750

# 保存PDF文件
c.save()
