import re

# file_path = r"C:\Users\<USER>\Desktop\调试工具\log\log202312151415.txt"
file_path = r"C:\Users\<USER>\Desktop\调试工具\log\log202312201650.txt"

file_path.encode('utf-8')


region_count=[80,69,84]


with open(file_path,'r', encoding='gbk') as f:
    log = f.read()

pattern = r"RELAY_CMD_ADHOC_RESULT_QUERY start.*?RELAY_CMD_ADHOC_RESULT_QUERY end"  
regions = re.findall(pattern, log, re.DOTALL)


pattern_cmd7 = r"^.*debug cmd7.*$"

target_lines = re.findall(pattern_cmd7, log, re.M)

result = []
for item in target_lines:
    new_item = item.replace(".","").split(' ')[-1]
    if new_item not in result:
        result.append(new_item)

print(result)

print(f"找到{len(regions)}个区域")

index=0

for i,region in enumerate(regions):
    found_with_count = len(re.findall(r"found", region))
    nd_count = len(re.findall(r"2nd", region))
    rd_count = len(re.findall(r"3rd", region))
    leaf_count = len(re.findall(r"leaf", region))
    target_id=target_lines[i].replace(".","").split(' ')[-1];


    if index<len(result) and target_id==result[index] and found_with_count==region_count[index]:
     nd_ratio = (nd_count)/ found_with_count*100
     rd_ratio = (rd_count)/ found_with_count*100
     leaf_ratio = (leaf_count)/ found_with_count*100
     
     print(f"区域{index+1}")
     print(f"优化器总数 {found_with_count} ")
     print(f"直连数 {nd_count},直连占比: {nd_ratio:.2f}")
     print(f"二级数 {rd_count}，二级占比: {rd_ratio:.2f}")
     print(f"三级数 {leaf_count}，三级占比: {leaf_ratio:.2f}")
     index=index+1