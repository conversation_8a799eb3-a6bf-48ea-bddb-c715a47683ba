import re

# file_path = r"C:\Users\<USER>\Desktop\szjcId\cg1\cg1_group.txt"
# file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\jg1_group.txt"
# file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\jg1.txt"
file_path = r"C:\Users\<USER>\Desktop\szjcId\jg2\jg1_one_group.txt"

# file_path = r"C:\Users\<USER>\Desktop\szjcId\全部\cg_by_all.txt"

file_path.encode('utf-8')

with open(file_path) as f:
    content = f.read()

pattern = r'(\d+-\d+-\d+):(.+),' 
data = {}
key_groups = []
current_prefix = None 
relay_values = []
for line in content.split('\n'):
    line= re.sub(pattern, r'\1:\2', line)
    # 获取最前面的前缀
    prefix = line.split('-')[0]
    # 判断前缀是否存在
    if prefix not in relay_values:
        key_groups.append([]) 
        relay_values.append(prefix)
    
    match = re.match(r'(\d+-\d+-\d+):(.*)', line)
    if match:
        key = match.group(1)
        values = [x.strip() for x in match.group(2).split(',')]
        data[key] = values
         # 把前缀相同的key放在一个数组中
        key_groups[relay_values.index(prefix)].append(key) 

 # 方法2:原地过滤，去除key_groups中的空值
key_groups = [group for group in key_groups if group] 
# print(key_groups)       

all_values = set()
all_values_index = {}
duplicates = {}

all_number=0
all_count=0
all_need=0
problem_group_name=[]
no_scan_group_name=[]

for j,key_group in enumerate(key_groups):
    content = ''
    real_group_len=0
    group_len=0
    group_name=[]
    key_index=''
    for key in key_group:
       content += f'//{key}\n'
       values = data[key]
       key_array=key.split("-")
       if len(values)==1 and len(values[0])==0:
           one_group_len=0
           no_scan_group_name.append(key);
       else:
           one_group_len=len(values)
       half_len=one_group_len//2
       real_group_len=real_group_len+one_group_len
       
       name_one_group_len=int(key_array[-1])

       group_len=group_len+name_one_group_len
       group_name.append(key_array[-2][1:])
       key_index=key_array[-3]
       all_number=all_number+name_one_group_len
       all_count=all_count+one_group_len

       diff_num=name_one_group_len-one_group_len
       if one_group_len!=0 and diff_num!=0:
           problem_group_name.append((key,diff_num))
   
       all_need=all_need+diff_num
       
       for i, value in enumerate(values):
         # 判断值是否存在，存在就判断是否存在于重复字典中，不存在就加入所有值中
          if value in all_values:
            # 判断值是否存在重复字典
            if value not in duplicates:
                duplicates[value]=all_values_index[value]
                duplicates[value].append((key,i+1))
            else:
                duplicates[value].append((key,i+1)) 
          else:    
            all_values.add(value)
            all_values_index[value]=[(key,i+1)]

            if i < half_len:
                content += f'{value} = [up{i+1}]\n'
            else:
                content +=f'{value} = [down{i-half_len+1}]\n'
       content += f'\n'
    # 写入文件
    # fname =r'C:\Users\<USER>\Desktop\调试工具\数据库\中继配置\szjc\szjc_relay_f00002'+str(j+34)+'_('+'_'.join(key_group) + ').txt' 
    fname =r'C:\Users\<USER>\Desktop\szjcId\jg2\jg单个组串\szjc_relay_f00'+key_index+'_('+'_'.join(group_name)+'@' +str(group_len)+'-'+str(real_group_len)+'-'+str(group_len-real_group_len)+ ').txt' 
    # fname =r'C:\Users\<USER>\Desktop\szjcId\cg1\szjc_relay_f000'+key_index+'_('+'_'.join(group_name)+'@' +str(group_len)+'-'+str(real_group_len)+'-'+str(group_len-real_group_len)+ ').txt' 
    # fname =r'C:\Users\<USER>\Desktop\szjcId\全部\szjc_relay_f00'+key_index+'_('+'_'.join(group_name)+'@' +str(group_len)+'-'+str(real_group_len)+'-'+str(group_len-real_group_len)+ ').txt' 

    with open(fname,'w',encoding="utf-8") as f:
       f.write(content)
# print(repeat_values)
# print(duplicates)

print('总数目:'+str(all_number))
print('已扫描:'+str(all_count))
print('不重复的数目:'+str(len(all_values)))
print('重复的数目:'+str(all_count-len(all_values)))
print('缺少的数目:'+str(all_number-len(all_values)))

print("完全没有扫描的组串:",no_scan_group_name)
print("数量不对的组串:",problem_group_name)

print("重复的id:")

for key in duplicates.keys():
    print(key, ":", duplicates[key])
