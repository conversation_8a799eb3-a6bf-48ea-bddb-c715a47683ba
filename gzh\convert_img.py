from PIL import Image
from env import GlobalVars
from pathlib import Path



def img_resized(old_img_path):


    # 打开图片文件
    img = Image.open(old_img_path)

    # 获取原始图片的尺寸
    original_width, original_height = img.size

    # 设置缩小的倍数
    scale_factor = 0.1  # 例如，0.5表示缩小到原来的50%

    # 计算新的尺寸
    new_width = int(original_width * scale_factor)
    new_height = int(original_height * scale_factor)

    # 修改图片尺寸
    img_resized = img.resize((new_width, new_height), Image.LANCZOS)
    # 替换原来的文件路径，生成新的文件路径
    new_img_path=Path(str(old_img_path).replace('img','img_test'))

    # 保存修改后的图片
    img_resized.save(new_img_path)
    print(new_img_path.name+" ok")


def main():
    GlobalVars.set_system_path()
    old_jingju_img_path = GlobalVars.base_jingju_path.joinpath('img')
    for entry in old_jingju_img_path.iterdir():
             new_entry_folder=Path(str(entry).replace('img','img_test'))
             # 创建转换文件夹目录
             new_entry_folder.mkdir(parents=True, exist_ok=True)
             # 把目录下所有的jpg文件路径转为list
             img_files=list(entry.glob('*.jpg'))
             for img_file in img_files:
                 img_resized(img_file)
             
  
     

if __name__ == "__main__":
    main()