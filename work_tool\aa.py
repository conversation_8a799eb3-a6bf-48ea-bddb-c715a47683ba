import xml.etree.ElementTree as ET
import re

xml = '''
<documents>
<document index="1">  
<source>aa.txt</source>
<document_content>1-1101-28:1,2,3,4,  
1-1102-28:6,7,8,
1-1103-28:9,11,12,  



2-1101-28:1,2,3,4,
3-1102-28:6,7,8, 
5-1103-28:9,11,12,</document_content>
</document> 
</documents>'''

root = ET.fromstring(xml)

prefix_input = input("请输入前缀:")


file_path = r"C:\Users\<USER>\Desktop\szjcId\全部\all.txt"

file_path.encode('utf-8')

with open(file_path) as f:
    content = f.read()

pattern = r'(\d+-\d+-\d+):(.+),' 
all_values = []
for line in content.split('\n'):
    line= re.sub(pattern, r'\1:\2', line)
    match = re.match(r'(\d+-\d+-\d+):(.*)', line)

    # 获取最前面的前缀
    prefix = line.split('-')[0]
    # 判断前缀是否存在
    if prefix==prefix_input and match:
        values = [x.strip() for x in match.group(2).split(',')]
        # print(len(values))
        # print(values)
        all_values=all_values+values

print(len(all_values))
print(all_values)