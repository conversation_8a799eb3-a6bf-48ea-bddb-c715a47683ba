import pandas as pd
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# 注册中文字体
font_name = 'SimSun'
font_path = 'SimSun.ttf'
pdfmetrics.registerFont(TTFont(font_name, font_path))

# 读取CSV文件
csv_file = '../input/title_id_update.csv'
df = pd.read_csv(csv_file)

# 创建PDF文件
pdf_file = '../output/jhok.pdf'
c = canvas.Canvas(pdf_file, pagesize=letter)

# 设置字体和大小
c.setFont(font_name, 12)

# 设置起始位置
x = 50
y = 750

# 特定的字符串
specific_string = "https://wx.zsxq.com/dweb2/index/topic_detail/"

# 遍历CSV文件的每一行
for index, row in df.iterrows():
    title = row[0]
    id = row[1]
    url = specific_string + str(id)

    # 绘制文本
    c.drawString(x, y, title.strip())

    # 添加超链接
    c.linkURL(url, (x, y - 4, x + c.stringWidth(title, font_name, 12), y + 8), relative=1, thickness=1, color=colors.blue)

    # 更新y坐标以在下一行绘制文本
    y -= 20

    # 如果到达页面底部，创建新页面
    if y < 50:
        c.showPage()
        c.setFont(font_name, 12)
        y = 750

# 保存PDF文件
c.save()
