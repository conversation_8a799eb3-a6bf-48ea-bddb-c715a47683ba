import csv
from urllib.parse import quote_plus
from datetime import datetime
currentDateAndTime = datetime.now()
currentTime = currentDateAndTime.strftime("%Y%m%d")


input_file = 'D:/works/input/wb_hot_title_'+currentTime+'.csv'
output_file =  'D:/works/input/wb_hot_url_'+currentTime+'.csv'
fixed_string = 'https://m.weibo.cn/search?containerid=100103type=1&q='

with open(input_file, 'r',encoding='utf-8') as infile, open(output_file, 'w',encoding='utf-8',newline='') as outfile:
    reader = csv.reader(infile)
    writer = csv.writer(outfile)
    for row in reader:
        for i in range(len(row)):
            encoded_str = fixed_string + quote_plus(row[i])
            row[i] = encoded_str
        writer.writerow(row)
