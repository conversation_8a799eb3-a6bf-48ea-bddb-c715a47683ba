import json

with open('../xhs/xhs_list.txt',encoding='utf-8') as f:
    data = json.load(f)

items = data['data']['items'] 

html = '''
<html>
<head>
<style>
table {
  border: 1px solid black; 
  border-collapse: collapse;
  margin: 0 auto;
}

td, th {
  border: 1px solid black;
  padding: 5px;
  text-align: center;
}
</style>  
</head>
<body>

<table>
<tr>
    <th>Index</th> 
    <th>Display Title</th>
    <th>Liked Count</th>
    <th>Nick Name</th>
</tr>
'''

index = 1  
for item in items:
    note_card = item['note_card']
    
    display_title = note_card['display_title']
    display_title_link = f"<a href='https://www.xiaohongshu.com/explore/{item['id']}' target='_blank'>{display_title}</a>"
    
    nick_name = note_card['user']['nick_name'] 
    user_id = note_card['user']['user_id']
    nick_name_link = f"<a href='https://www.xiaohongshu.com/user/profile/{user_id}' target='_blank'>{nick_name}</a>"
    
    liked_count = note_card['interact_info']['liked_count']
    
    html += f'''
<tr>
    <td>{index}</td>
    <td>{display_title_link}</td> 
    <td>{liked_count}</td>
    <td>{nick_name_link}</td>
</tr>
'''
    index += 1
    
html += '''
</table> 
</body>
</html>
'''

with open('../xhs/output.html', 'w',encoding='utf-8') as f:
    f.write(html)