import pandas as pd
from ebooklib import epub

# 读取CSV文件
df = pd.read_csv('../input/title_id_update.csv')

# 创建EPUB电子书对象
book = epub.EpubBook()

# 设置书名、作者等元数据
book.set_title('My EBook')
book.add_author('Your Name')

# 创建目录
toc = []

# 遍历CSV文件的每一行
for index, row in df.iterrows():
    title = row[0]
    id = row[1]

    # 创建章节对象
    chapter = epub.EpubHtml(title=title, file_name=f'chapter_{index}.xhtml')

    # 生成标题的超链接
    content = f'<a href="https://wx.zsxq.com/dweb2/index/topic_detail/{id}_link">{title}</a>'
    chapter.content = f'<html><body>{content}</body></html>'

    # 添加章节到电子书
    book.add_item(chapter)

    # 添加目录项
    toc.append(epub.Link(f'chapter_{index}.xhtml', title))

# 设置目录
book.toc = (epub.Link('toc.xhtml', 'Table of Contents'), (toc,))

# 生成EPUB文件
epub.write_epub('../output/output.epub', book, {})

print('EPUB文件生成成功！')
